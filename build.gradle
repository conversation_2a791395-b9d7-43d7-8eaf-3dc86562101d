plugins {
    id 'java'
    id 'org.springframework.boot' version '3.3.0'
    id 'io.spring.dependency-management' version '1.1.5'
    id 'org.liquibase.gradle' version '2.2.2'
}

group = 'pro.civil-aviation'
version = '0.0.1-SNAPSHOT'

java {
    sourceCompatibility = '21'
}

configurations {
    compileOnly {
        extendsFrom annotationProcessor
    }
}

repositories {
    mavenCentral()
}

dependencies {
    implementation 'org.springframework.boot:spring-boot-starter-oauth2-authorization-server'
//    implementation 'org.springframework.boot:spring-boot-starter-oauth2-client'
    implementation 'org.springframework.boot:spring-boot-starter-security'
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'org.springframework.boot:spring-boot-starter-data-redis'
    implementation 'org.springframework.session:spring-session-data-redis'
    implementation 'org.springframework.boot:spring-boot-starter-thymeleaf'
    implementation 'org.apache.commons:commons-pool2:2.12.0'
    implementation 'commons-codec:commons-codec:1.15'
    runtimeOnly 'org.postgresql:postgresql'

    compileOnly 'org.projectlombok:lombok'
    annotationProcessor('org.hibernate:hibernate-jpamodelgen:6.5.2.Final')
    annotationProcessor 'org.springframework.boot:spring-boot-configuration-processor'
    annotationProcessor 'org.projectlombok:lombok'

    liquibaseRuntime 'org.liquibase:liquibase-core'
    liquibaseRuntime 'org.liquibase:liquibase-groovy-dsl:3.0.2'
    liquibaseRuntime 'info.picocli:picocli:4.6.1'
    liquibaseRuntime 'org.postgresql:postgresql'

    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'org.springframework.boot:spring-boot-testcontainers'
    testImplementation 'org.springframework.security:spring-security-test'
    testImplementation 'org.testcontainers:junit-jupiter'
    testRuntimeOnly 'org.junit.platform:junit-platform-launcher'
    compileOnly 'org.mapstruct:mapstruct:1.5.3.Final'
    annotationProcessor 'org.mapstruct:mapstruct-processor:1.5.3.Final'
    annotationProcessor 'org.projectlombok:lombok-mapstruct-binding:0.2.0'
}

tasks.named('test') {
    useJUnitPlatform()
}

jar.enabled = true

apply from: 'schemas.gradle'

liquibase {
    activities {
        // No need to filter for "common" as it doesn't exist in our schemas
        schemas.each { schema ->
            "${schema.name}" {
                changelogFile '/src/main/resources/liquibase/changelog_cumulative.xml'
                url schema.url
                username schema.username
                password schema.password
                defaultSchemaName schema.name
                logLevel 'info'
            }
        }
    }
}
