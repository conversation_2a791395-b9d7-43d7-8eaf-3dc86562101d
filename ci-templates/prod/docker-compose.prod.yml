services:
  api-gateway:
    image: capcapcap/api-gateway:0.1.3
    container_name: api-gateway
    restart: always
    networks:
      - capnet

  core-backend:
    image: capcapcap/core-backend:0.1.3
    container_name: core-backend
    restart: always
    env_file:
      - ../../.env
    networks:
      - capnet

  auth-server:
    image: capcapcap/auth-server:0.1.3
    container_name: auth-server
    restart: always
    env_file:
      - ../../.env
    networks:
      - capnet

  cap-ui:
    image: capcapcap/cap-ui:0.1.3.1
    container_name: cap-ui
    restart: always
    networks:
      - capnet

  redis:
    image: redis:7
    container_name: redis
    restart: always
    command: ["redis-server", "--appendonly", "yes"]
    volumes:
      - redis-data:/data
    networks:
      - capnet

networks:
  capnet:
    name: capnet
    driver: bridge
    external: true

volumes:
  redis-data:
