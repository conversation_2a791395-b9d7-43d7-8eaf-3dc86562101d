# Use a minimal JRE image
FROM eclipse-temurin:21-jre-alpine

# Add metadata
LABEL org.opencontainers.image.authors="Civil Aviation Platform"
LABEL org.opencontainers.image.version="0.1.3"
LABEL org.opencontainers.image.title="CAP Authorization Server"
LABEL org.opencontainers.image.description="Authorization server for the Civil Aviation Platform"

# Create app directory
WORKDIR /app

# Copy the pre-built JAR file
COPY build/libs/*.jar app.jar

# Expose the application port
EXPOSE 8082

# Set environment variables
ENV COOKIE_DOMAIN=civil-aviation.pro
ENV SPRING_SECURITY_OAUTH2_RESOURCESERVER_JWT_ISSUER_URI=http://auth.civil-aviation.pro

# Run the application with debug options
ENTRYPOINT ["java", "-Dlogging.level.org.springframework.security=DEBUG", "-Dlogging.level.pro.civilaviation=DEBUG", "-jar", "app.jar"]
