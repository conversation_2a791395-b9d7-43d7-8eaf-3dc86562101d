@startuml
actor user
participant "api-gateway" as agw
participant "authorization-server" as idp
participant "resource-server" as rs

user -> agw ++ : 1. GET http://api-gateway/protected-resource
user <-- agw -- : 2. 302 Found http://api-gateway/oauth2/authorization/civil-aviation-api-gateway

user -> agw ++ : 3. GET http://api-gateway/oauth2/authorization/civil-aviation-api-gateway
user <-- agw -- : 4. 302 Found http://auth-server/oauth2/authorize + oidc parameters for request authorization_code

user -> idp ++ : 5. GET http://auth-server/oauth2/authorize + oidc parameters for request authorization_code
user <-- idp -- : 6. 302 Found http://auth-server/login

user -> idp ++ : 7. GET http://auth-server/login
user <-- idp -- : 8. 200 OK + login page

user -> idp ++ : 9. POST http://auth-server/login + credentials
user <-- idp -- : 10. 302 Found http://auth-server/oauth2/authorize

user -> idp ++ : 11. GET http://auth-server/oauth2/authorize
user <-- idp -- : 12. 302 Found http://api-gateway/login/oauth2/code/civil-aviation-api-gateway + authorization_code

user -> agw ++ : 13. GET http://api-gateway/login/oauth2/code/civil-aviation-api-gateway + authorization_code
agw -> idp ++ : 14. POST http://auth-server/oauth2/token + authorization_code
agw <-- idp -- : 15. 200 OK + access_token
agw -> agw : 16. store access_token in session
user <-- agw -- : 17. 302 Found http://api-gateway/protected-resource + session_id

user -> agw ++ : 18. GET http://api-gateway/protected-resource + session_id
agw -> agw : 19. get access_token from session
agw -> rs ++ : 20. GET http://resource-server/protected-resource + access_token
rs --> rs : 21. validate access_token
agw <-- rs -- : 22. 200 OK + resource
user <-- agw -- : 23. 200 OK + resource
@enduml