package pro.civilaviation.authorizationserver;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.test.context.TestConfiguration;

@TestConfiguration(proxyBeanMethods = false)
public class TestAuthorizationServerApplication {

    public static void main(String[] args) {
        SpringApplication.from(AuthorizationServerApplication::main).with(TestAuthorizationServerApplication.class).run(args);
    }

}
