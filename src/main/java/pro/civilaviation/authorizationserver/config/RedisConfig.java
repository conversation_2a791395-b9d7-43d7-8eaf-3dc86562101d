package pro.civilaviation.authorizationserver.config;

import io.lettuce.core.ClientOptions;
import io.lettuce.core.SslOptions;
import lombok.RequiredArgsConstructor;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettucePoolingClientConfiguration;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.oauth2.server.authorization.OAuth2Authorization;
import org.springframework.util.StringUtils;
import pro.civilaviation.authorizationserver.config.data.RedisConfigData;

import java.io.File;

/**
 * created by Alexandr_Nikolskiy
 */

@RequiredArgsConstructor
@Configuration(proxyBeanMethods = false)
public class RedisConfig {

    private final RedisConfigData redisConfigData;

    @Bean
    public RedisTemplate<String, OAuth2Authorization> redisTemplate(LettuceConnectionFactory connectionFactory) {
        RedisTemplate<String, OAuth2Authorization> redisTemplate = new RedisTemplate<>();
        redisTemplate.setConnectionFactory(connectionFactory);
        redisTemplate.setEnableTransactionSupport(true);

        return redisTemplate;
    }


    @Bean
    public LettuceConnectionFactory connectionFactory(LettucePoolingClientConfiguration clientConfiguration) {
        RedisStandaloneConfiguration redisStandaloneConfiguration = new RedisStandaloneConfiguration();
        redisStandaloneConfiguration.setHostName(redisConfigData.host());
        redisStandaloneConfiguration.setPort(redisConfigData.port());
        redisStandaloneConfiguration.setDatabase(redisConfigData.databaseIndex());
        if (StringUtils.hasText(redisConfigData.username())) {
            redisStandaloneConfiguration.setUsername(redisConfigData.username());
        }
        if (StringUtils.hasText(redisConfigData.password())) {
            redisStandaloneConfiguration.setPassword(redisConfigData.password());
        }
        LettuceConnectionFactory connectionFactory =
                new LettuceConnectionFactory(redisStandaloneConfiguration, clientConfiguration);
        connectionFactory.setShareNativeConnection(false);

        return connectionFactory;
    }

    @Bean
    public LettucePoolingClientConfiguration lettuceClientConfiguration (ClientOptions clientOptions) {
        LettucePoolingClientConfiguration.LettucePoolingClientConfigurationBuilder
                clientConfigurationBuilder = LettucePoolingClientConfiguration.builder();

        GenericObjectPoolConfig<Object> poolConfig = new GenericObjectPoolConfig<>();
        poolConfig.setMaxTotal(redisConfigData.maxTotal());
        poolConfig.setMinIdle(redisConfigData.minIdle());

        clientConfigurationBuilder
                .poolConfig(poolConfig)
                .clientOptions(clientOptions);

        if (redisConfigData.sslEnabled()) {
            clientConfigurationBuilder.useSsl();
        }

        return clientConfigurationBuilder.build();
    }

    @Bean
    public ClientOptions lettuceClientOptions (SslOptions sslOptions) {
        ClientOptions.Builder clientOptionsBuilder = ClientOptions.builder();
        if (redisConfigData.sslEnabled()) {
            clientOptionsBuilder.sslOptions(sslOptions);
        }
        return clientOptionsBuilder.build();
    }

    @Bean
    public  SslOptions lettuceSslOptions () {
        SslOptions.Builder sslOptionsBuilder = SslOptions.builder();
        if (redisConfigData.sslEnabled()) {
            sslOptionsBuilder
                    .keystore(new File(redisConfigData.keystoreLocation()))
                    .truststore(new File(redisConfigData.truststoreLocation()));

        }
        return sslOptionsBuilder.build();
    }
}
