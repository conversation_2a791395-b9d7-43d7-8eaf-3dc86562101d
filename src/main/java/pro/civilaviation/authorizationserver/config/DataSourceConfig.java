package pro.civilaviation.authorizationserver.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DriverManagerDataSource;
import org.springframework.util.Assert;
import pro.civilaviation.authorizationserver.config.data.TenantConfigData;
import pro.civilaviation.authorizationserver.multitent.MultitenantRoutingDataSource;
import pro.civilaviation.authorizationserver.multitent.TenantContext;
import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;

/**
 * created by <PERSON><PERSON><PERSON>_Nikols<PERSON>y
 */

@Configuration
public class DataSourceConfig {

    private final TenantConfigData tenantConfigData;

    public DataSourceConfig(TenantConfigData tenantConfigData) {
        Assert.notNull(tenantConfigData, "tenantConfigData cannot be null");
        this.tenantConfigData = tenantConfigData;
    }

    @Bean
    public DataSource dataSource () {
        MultitenantRoutingDataSource multitenantRoutingDataSource = new MultitenantRoutingDataSource();
        Map<Object, Object> dataSourceMap = new HashMap<>();

        tenantConfigData.tenants().forEach((tenantId, tc)
                -> dataSourceMap.put(tc.getDatasourceSchema(), createDataSource(tc)));

        multitenantRoutingDataSource.setTargetDataSources(dataSourceMap);
        multitenantRoutingDataSource.afterPropertiesSet();
        return multitenantRoutingDataSource;
    }

    private DataSource createDataSource(TenantContext tc) {
        DriverManagerDataSource dataSource = new DriverManagerDataSource();
        dataSource.setDriverClassName("org.postgresql.Driver");
        dataSource.setUrl(tc.getDatasourceUrl());
        dataSource.setSchema(tc.getDatasourceSchema());
        dataSource.setUsername(tc.getDatasourceUsername());
        dataSource.setPassword(tc.getDatasourcePassword());

        return dataSource;
    }
}
