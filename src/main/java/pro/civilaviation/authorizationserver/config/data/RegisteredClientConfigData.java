package pro.civilaviation.authorizationserver.config.data;

import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.List;

/**
 * created by <PERSON><PERSON><PERSON><PERSON>
 */

@ConfigurationProperties(prefix ="registered-client-config")
public record RegisteredClientConfigData (
        String clientId,
        String clientSecret,
        String redirectUri,
        List<String> additionalRedirectUris,
        String postLogoutRedirectUri
) {}
