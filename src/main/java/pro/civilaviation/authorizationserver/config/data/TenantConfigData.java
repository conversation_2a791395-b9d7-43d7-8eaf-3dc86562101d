package pro.civilaviation.authorizationserver.config.data;

import org.springframework.boot.context.properties.ConfigurationProperties;
import pro.civilaviation.authorizationserver.multitent.TenantContext;
import java.util.Map;
import java.util.UUID;


/**
 * created by <PERSON><PERSON><PERSON>_Nikols<PERSON>y
 */

/**
 * !!! when using tenatConfigData.tenants.id() in spring beans,
 * make sure the bean loading depends on TenantService.
 *
 * TenatService perfom post construct method fillTenantIds.
 */

    @ConfigurationProperties(prefix = "tenant-config")
public record TenantConfigData(Map<UUID, TenantContext> tenants) {
}
