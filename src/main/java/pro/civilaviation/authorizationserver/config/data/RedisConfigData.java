package pro.civilaviation.authorizationserver.config.data;

import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * created by <PERSON><PERSON><PERSON><PERSON>
 */

@ConfigurationProperties(prefix = "redis-config")
public record RedisConfigData(
        String host,
        Integer port,
        String username,
        String password,
        Integer databaseIndex,
        Boolean sslEnabled,
        String keystoreLocation,
        String truststoreLocation,
        Integer maxTotal,
        Integer minIdle
        ) {}
