package pro.civilaviation.authorizationserver.config;

import org.apache.commons.codec.binary.Hex;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.crypto.encrypt.Encryptors;
import org.springframework.security.crypto.encrypt.TextEncryptor;

import static java.nio.charset.StandardCharsets.UTF_8;

/**
 * created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 */

@Configuration
public class KeyConfig {

    @Bean
    public TextEncryptor textEncryptor(@Value("${jwt.encryptor.password:cap}") String password,
                                       @Value("${jwt.encryptor.salt:cap") String salt) {
        String saltHex = generateSecureSalt(salt);

        return Encryptors.text(password, saltHex);
    }

    private String generateSecureSalt(String salt) {
        return Hex.encodeHexString(salt.getBytes(UTF_8));
    }
}
