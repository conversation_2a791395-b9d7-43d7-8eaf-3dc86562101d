package pro.civilaviation.authorizationserver.config;

import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import pro.civilaviation.authorizationserver.config.data.CorsConfigData;

import java.util.Arrays;
import java.util.Collections;

/**
 * created by <PERSON><PERSON><PERSON>_<PERSON>
 */

@Configuration
@RequiredArgsConstructor
public class CorsConfig {

    private final CorsConfigData corsConfigData;

    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        CorsConfiguration config = new CorsConfiguration();

        // Allow all headers
        config.addAllowedHeader("*");

        // Explicitly add common headers
        config.addExposedHeader("Authorization");
        config.addExposedHeader("Content-Type");
        config.addExposedHeader("Accept");
        config.addExposedHeader("Origin");
        config.addExposedHeader("X-Requested-With");
        config.addExposedHeader("Access-Control-Allow-Origin");
        config.addExposedHeader("Access-Control-Allow-Credentials");
        config.addExposedHeader("Access-Control-Allow-Headers");

        // Allow all methods including OPTIONS for preflight
        config.addAllowedMethod(HttpMethod.GET.name());
        config.addAllowedMethod(HttpMethod.POST.name());
        config.addAllowedMethod(HttpMethod.PUT.name());
        config.addAllowedMethod(HttpMethod.DELETE.name());
        config.addAllowedMethod(HttpMethod.OPTIONS.name());
        config.addAllowedMethod(HttpMethod.HEAD.name());
        config.addAllowedMethod(HttpMethod.PATCH.name());

        // Allow specified origins from configuration
        config.setAllowedOrigins(corsConfigData.allowedOrigins());

        // Allow credentials (cookies, authorization headers, etc.)
        config.setAllowCredentials(true);

        // When using setAllowCredentials(true), we can't use "*" for allowed origins
        // Instead, use allowedOriginPatterns if no specific origins are configured
        if (config.getAllowedOrigins() == null || config.getAllowedOrigins().isEmpty()) {
            config.setAllowedOriginPatterns(Collections.singletonList("*"));
        } else {
            // Make sure we're not also using "*" as an allowed origin when credentials are allowed
            config.getAllowedOrigins().remove("*");

            // Add specific patterns for localhost with any port
//            config.addAllowedOriginPattern("http://localhost:[*]");
//            config.addAllowedOriginPattern("http://civil-aviation.pro:[*]");
        }

        // Set max age for preflight requests (in seconds)
        config.setMaxAge(3600L);

        // Register the configuration for all paths
        source.registerCorsConfiguration("/**", config);
        return source;
    }
}
