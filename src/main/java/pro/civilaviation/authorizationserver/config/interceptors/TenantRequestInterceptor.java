package pro.civilaviation.authorizationserver.config.interceptors;

import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;
import pro.civilaviation.authorizationserver.multitent.TenantContext;
import pro.civilaviation.authorizationserver.multitent.TenantContextHolder;

import java.io.IOException;

import static pro.civilaviation.authorizationserver.multitent.TenantFilter.TENANT_ID_HEADER;


/**
 * created by <PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>
 */

public class TenantRequestInterceptor implements ClientHttpRequestInterceptor {

    @Override
    public ClientHttpResponse intercept(HttpRequest request, byte[] body, ClientHttpRequestExecution execution) throws IOException {
        TenantContext currentTenant = TenantContextHolder.getCurrentTenant();
        if (currentTenant == null) {
            throw new IllegalStateException("currentTenant cannot be null");
        }
        request.getHeaders().add(TENANT_ID_HEADER, currentTenant.getId().toString());
        return execution.execute(request, body);
    }
}
