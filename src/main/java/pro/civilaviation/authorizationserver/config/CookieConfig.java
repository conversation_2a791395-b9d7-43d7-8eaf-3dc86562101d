package pro.civilaviation.authorizationserver.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.session.web.http.CookieSerializer;
import org.springframework.session.web.http.DefaultCookieSerializer;

/**
 * created by <PERSON><PERSON><PERSON>_<PERSON>
 */

@Configuration
public class CookieConfig {

    @Bean
    public CookieSerializer cookieSerializer() {
        DefaultCookieSerializer serializer = new DefaultCookieSerializer();
        serializer.setCookieName("sauth");
        serializer.setCookiePath("/");
        serializer.setSameSite("Strict");
        return serializer;
    }
}
