package pro.civilaviation.authorizationserver.multitent;


/**
 * created by <PERSON><PERSON><PERSON>_Nikolskiy
 */

public class TenantContextHolder {

    private final static ThreadLocal<TenantContext> currentTenant = new InheritableThreadLocal<>();

    public static TenantContext getCurrentTenant() {
       return currentTenant.get();
    }

    public static void setCurrentTenant(TenantContext context){
        if (context == null) {
            throw new IllegalStateException("tenantContext cannot be null");
        }
        currentTenant.set(context);

    }

    public static void clear() {
        currentTenant.remove();
    }

}
