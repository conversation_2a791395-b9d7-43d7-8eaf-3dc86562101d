package pro.civilaviation.authorizationserver.multitent;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.web.filter.OncePerRequestFilter;
import pro.civilaviation.authorizationserver.config.data.TenantConfigData;

import java.io.IOException;
import java.util.UUID;

import static org.springframework.core.Ordered.HIGHEST_PRECEDENCE;

/**
 * created by <PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>
 */

@Order(HIGHEST_PRECEDENCE)
@Component
public class TenantFilter extends OncePerRequestFilter {

    private static final Logger logger = LoggerFactory.getLogger(TenantFilter.class);

    public static final String TENANT_ID_HEADER = "x-tenant-id";
    private final TenantConfigData tenantConfigData;

    public TenantFilter(TenantConfigData tenantConfigData) {
        Assert.notNull(tenantConfigData, "tenantConfigData cannot be null");
        this.tenantConfigData = tenantConfigData;
    }

    @Override
    protected boolean shouldNotFilter(HttpServletRequest request) {
        String path = request.getServletPath();
        // Skip tenant filter for public endpoints and OPTIONS requests
        return "OPTIONS".equalsIgnoreCase(request.getMethod());
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request,
                                    HttpServletResponse response,
                                    FilterChain filterChain) throws ServletException, IOException {
        String tenantId = request.getHeader(TENANT_ID_HEADER);
        String path = request.getServletPath();
        logger.debug("Processing request for path: {}, tenant header: {}", path, tenantId);

        // If tenant ID header is missing, use the first tenant as default
        if (tenantId == null || tenantId.isEmpty()) {
            if (!tenantConfigData.tenants().isEmpty()) {
                // Get the first tenant from the map
                TenantContext defaultTenant = tenantConfigData.tenants().values().iterator().next();
                TenantContextHolder.setCurrentTenant(defaultTenant);
            } else {
                // No tenants configured, return 400 Bad Request
                response.sendError(HttpServletResponse.SC_BAD_REQUEST, "No tenant ID provided and no default tenant available");
                return;
            }
        } else {
            try {
                UUID uuid = UUID.fromString(tenantId);
                TenantContext tenantContext = tenantConfigData.tenants().get(uuid);

                if (tenantContext == null) {
                    // Tenant ID not found, return 400 Bad Request
                    logger.error("Invalid tenant ID: {}", tenantId);
                    response.sendError(HttpServletResponse.SC_BAD_REQUEST, "Invalid tenant ID");
                    return;
                }

                logger.debug("Using tenant from header: {}", uuid);
                TenantContextHolder.setCurrentTenant(tenantContext);
            } catch (IllegalArgumentException e) {
                // Invalid UUID format, return 400 Bad Request
                logger.error("Invalid tenant ID format: {}", tenantId, e);
                response.sendError(HttpServletResponse.SC_BAD_REQUEST, "Invalid tenant ID format");
                return;
            }
        }

        try {
            // Ensure tenant context is set before proceeding
            if (TenantContextHolder.getCurrentTenant() == null) {
                logger.warn("Tenant context is null before filter chain, setting default tenant");
                TenantContextHolder.setCurrentTenant(TenantContextHolder.getCurrentTenant()); // This will use the default tenant
            }

            filterChain.doFilter(request, response);
        } catch (Exception e) {
            logger.error("Error in tenant filter: {}", e.getMessage(), e);
            throw e;
        } finally {
            TenantContextHolder.clear();
        }
    }
}
