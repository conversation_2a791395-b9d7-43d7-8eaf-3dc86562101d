package pro.civilaviation.authorizationserver.multitent;

import org.springframework.jdbc.datasource.lookup.AbstractRoutingDataSource;

/**
 * created by <PERSON><PERSON>r_Nikols<PERSON>y
 */

public class MultitenantRoutingDataSource extends AbstractRoutingDataSource {

    @Override
    protected String determineCurrentLookupKey() {
        TenantContext tenantContext = TenantContextHolder.getCurrentTenant();
        if (tenantContext == null) {
            throw new IllegalStateException("tenantContext cannot be null");
        }
        return tenantContext.getDatasourceSchema();
    }
}
