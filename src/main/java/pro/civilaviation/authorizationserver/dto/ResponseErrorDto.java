package pro.civilaviation.authorizationserver.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.time.Instant;

/**
 * created by <PERSON><PERSON><PERSON>_<PERSON>
 */

@JsonIgnoreProperties(ignoreUnknown = true)
public record ResponseErrorDto(Instant timestamp,
                               Integer status,
                               String error,
                               String message,
                               String path)
{}