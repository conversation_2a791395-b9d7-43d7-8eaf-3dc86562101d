package pro.civilaviation.authorizationserver.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.Set;
import java.util.UUID;

/**
 * created by <PERSON>andr_Nikols<PERSON>y
 */

@JsonIgnoreProperties(ignoreUnknown = true)
public record RequestUsersFilterDto(
        UUID id,
        String username,
        Boolean isAccountLocked,
        Set<String> authorities
) {}