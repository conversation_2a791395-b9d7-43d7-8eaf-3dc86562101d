package pro.civilaviation.authorizationserver.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.Set;

/**
 * DTO for {@link pro.civilaviation.authorizationserver.domain.entity.UserEntity}
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public record RequestUpdateUserDto(String username,
                                   String password,
                                   Boolean isAccountLocked,
                                   Set<String> authorities) {
}