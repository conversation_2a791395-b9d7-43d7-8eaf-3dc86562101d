package pro.civilaviation.authorizationserver.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.Set;
import java.util.UUID;

/**
 * DTO for {@link pro.civilaviation.authorizationserver.domain.entity.UserEntity}
 */

@JsonIgnoreProperties(ignoreUnknown = true)
public record ResponseUserDto(
        UUID id,
        String username,
        Boolean isAccountLocked,
        Set<String> authorities) {
}