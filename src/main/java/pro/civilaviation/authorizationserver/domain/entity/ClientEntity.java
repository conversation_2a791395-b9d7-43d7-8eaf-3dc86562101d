package pro.civilaviation.authorizationserver.domain.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.hibernate.proxy.HibernateProxy;

import java.time.Instant;
import java.util.Objects;

/**
 * created by <PERSON><PERSON><PERSON><PERSON>
 */

@Getter
@Setter
@Entity
@Accessors(chain = true)
@Table(name = "`client`")
public class ClientEntity {
    @Id
    @Column(name = "id", nullable = false)
    private String id;

    @Column(name = "client_id")
    private String clientId;

    @Column(name = "client_id_issued_at")
    private Instant clientIdIssuedAt;

    @Column(name = "client_secret")
    private String clientSecret;

    @Column(name = "client_secret_expires_at")
    private Instant clientSecretExpiresAt;

    @Column(name = "client_name")
    private String clientName;

    @Column(name = "client_authentication_methods", length = 1000)
    private String clientAuthenticationMethods;

    @Column(name = "authorization_grant_types", length = 1000)
    private String authorizationGrantTypes;

    @Column(name = "redirect_uris", length = 1000)
    private String redirectUris;

    @Column(name = "post_logout_redirect_uris", length = 1000)
    private String postLogoutRedirectUris;

    @Column(name = "scopes", length = 1000)
    private String scopes;

    @Column(name = "client_settings", length = 2000)
    private String clientSettings;

    @Column(name = "token_settings", length = 2000)
    private String tokenSettings;

    @Override
    public final boolean equals(Object o) {
        if (this == o) return true;
        if (o == null) return false;
        Class<?> oEffectiveClass = o instanceof HibernateProxy ? ((HibernateProxy) o).getHibernateLazyInitializer().getPersistentClass() : o.getClass();
        Class<?> thisEffectiveClass = this instanceof HibernateProxy ? ((HibernateProxy) this).getHibernateLazyInitializer().getPersistentClass() : this.getClass();
        if (thisEffectiveClass != oEffectiveClass) return false;
        ClientEntity that = (ClientEntity) o;
        return getId() != null && Objects.equals(getId(), that.getId());
    }

    @Override
    public final int hashCode() {
        return this instanceof HibernateProxy ? ((HibernateProxy) this).getHibernateLazyInitializer().getPersistentClass().hashCode() : getClass().hashCode();
    }

    @Override
    public String toString() {
        return getClass().getSimpleName() + "(" +
                "id = " + id + ", " +
                "tokenSettings = " + tokenSettings + ", " +
                "clientSettings = " + clientSettings + ", " +
                "scopes = " + scopes + ", " +
                "postLogoutRedirectUris = " + postLogoutRedirectUris + ", " +
                "redirectUris = " + redirectUris + ", " +
                "authorizationGrantTypes = " + authorizationGrantTypes + ", " +
                "clientAuthenticationMethods = " + clientAuthenticationMethods + ", " +
                "clientName = " + clientName + ", " +
                "clientSecretExpiresAt = " + clientSecretExpiresAt + ", " +
                "clientIdIssuedAt = " + clientIdIssuedAt + ", " +
                "clientId = " + clientId + ")";
    }
}