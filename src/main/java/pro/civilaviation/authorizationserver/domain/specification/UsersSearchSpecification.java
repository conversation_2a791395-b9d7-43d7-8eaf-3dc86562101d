package pro.civilaviation.authorizationserver.domain.specification;


import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.Predicate;
import org.springframework.data.jpa.domain.Specification;
import pro.civilaviation.authorizationserver.domain.entity.AuthorityEntity;
import pro.civilaviation.authorizationserver.domain.entity.AuthorityEntity_;
import pro.civilaviation.authorizationserver.domain.entity.UserEntity;
import pro.civilaviation.authorizationserver.domain.entity.UserEntity_;
import pro.civilaviation.authorizationserver.dto.RequestUsersFilterDto;

import java.util.Set;
import java.util.UUID;


/**
 * created by Alexandr_Nikolskiy
 */

public class UsersSearchSpecification {

    public static Specification<UserEntity> hasUsernameLike(String usernameToSearch) {
        return (root, query, criteriaBuilder) -> {
            if (usernameToSearch == null || usernameToSearch.isEmpty()) {
                return criteriaBuilder.conjunction();
            }
            return criteriaBuilder.like(root.get(UserEntity_.USERNAME), usernameToSearch.trim());
        };
    }

    public static Specification<UserEntity> isAccountLocked(Boolean isAccountLocked) {
        return (root, query, criteriaBuilder) -> {
            if(isAccountLocked == null) {
                return criteriaBuilder.conjunction();
            }
            return criteriaBuilder.equal(root.get(UserEntity_.IS_ACCOUNT_LOCKED), isAccountLocked);
        };
    }

    public static Specification<UserEntity> hasUserId(UUID userIdToSearch) {
        return (root, query, criteriaBuilder) -> {
            if (userIdToSearch == null) {
                return criteriaBuilder.conjunction();
            }
            return criteriaBuilder.equal(root.get(UserEntity_.ID), userIdToSearch);
        };
    }

    public static Specification<UserEntity> hasAuthorities(Set<String> authoritiesToSearch) {
        return (root, query, criteriaBuilder) -> {
            if(authoritiesToSearch == null || authoritiesToSearch.isEmpty()) {
                return criteriaBuilder.conjunction();
            }
            Join<UserEntity, AuthorityEntity> authoritiesJoin = root.join(UserEntity_.AUTHORITY_ENTITIES);
            Predicate authorityPredicate = authoritiesJoin.get(AuthorityEntity_.authority).in(authoritiesToSearch);

            query.groupBy(root.get(UserEntity_.ID));
            query.having(criteriaBuilder.equal(
                    criteriaBuilder.countDistinct(authoritiesJoin.get(AuthorityEntity_.AUTHORITY)),
                    authoritiesToSearch.size()));
            return authorityPredicate;
        };
    }

    public static Specification<UserEntity> withAllFilters(RequestUsersFilterDto requestUsersFilterDto) {
        return Specification.allOf(
                        UsersSearchSpecification.hasUserId(requestUsersFilterDto.id()),
                        UsersSearchSpecification.hasUsernameLike(requestUsersFilterDto.username()),
                        UsersSearchSpecification.isAccountLocked(requestUsersFilterDto.isAccountLocked()),
                        UsersSearchSpecification.hasAuthorities(requestUsersFilterDto.authorities())
                );
    }
}