package pro.civilaviation.authorizationserver.domain.converter;

import jakarta.persistence.Converter;
import jakarta.persistence.AttributeConverter;
import lombok.RequiredArgsConstructor;
import org.springframework.security.crypto.encrypt.TextEncryptor;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.interfaces.RSAPrivateKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.Base64;

@Converter
@RequiredArgsConstructor
public class RSAPrivateKeyConverter implements AttributeConverter<RSAPrivateKey, String> {

    private static final String PRIVATE_KEY_PREFIX = "-----BEGIN PRIVATE KEY-----";
    private static final String PRIVATE_KEY_SUFFIX = "-----END PRIVATE KEY-----";

    private final TextEncryptor textEncryptor;

    @Override
    public String convertToDatabaseColumn(RSAPrivateKey rsaPrivateKey){
        try {
            if (rsaPrivateKey == null) {
                return null;
            }

            PKCS8EncodedKeySpec pkcs8EncodedKeySpec = new PKCS8EncodedKeySpec(rsaPrivateKey.getEncoded());
            String encodedKey = Base64.getMimeEncoder()
                    .encodeToString(pkcs8EncodedKeySpec.getEncoded());
            String pemFormattedKey = PRIVATE_KEY_PREFIX + "\n" + encodedKey + "\n" + PRIVATE_KEY_SUFFIX;

            return textEncryptor.encrypt(pemFormattedKey);
        } catch (Exception e) {
            throw new IllegalArgumentException("Failed to convert RSAPrivateKey to encrypted String.", e);
        }
    }

    @Override
    public RSAPrivateKey convertToEntityAttribute(String dbData) {
        try {
            if (dbData == null || dbData.isEmpty()) {
                return null;
            }

            String decryptedData = textEncryptor.decrypt(dbData);
            String encodedKey = decryptedData
                    .replace(PRIVATE_KEY_PREFIX, "")
                    .replace(PRIVATE_KEY_SUFFIX, "")
                    .replaceAll("\\s", "");

            byte[] keyBytes = Base64.getMimeDecoder().decode(encodedKey);
            PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(keyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            PrivateKey privateKey = keyFactory.generatePrivate(keySpec);

            return (RSAPrivateKey) privateKey;
        } catch (Exception e) {
            throw new IllegalArgumentException("Failed to convert encrypted String to RSAPrivateKey.", e);
        }
    }
}
