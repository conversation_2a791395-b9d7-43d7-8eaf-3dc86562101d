package pro.civilaviation.authorizationserver.domain.converter;

import jakarta.persistence.Converter;
import jakarta.persistence.AttributeConverter;
import lombok.RequiredArgsConstructor;
import org.springframework.security.crypto.encrypt.TextEncryptor;
import java.security.KeyFactory;
import java.security.PublicKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;

@Converter
@RequiredArgsConstructor
public class RSAPublicKeyConverter implements AttributeConverter<RSAPublicKey, String> {

    private static final String PUBLIC_KEY_PREFIX = "-----BEGIN PUBLIC KEY-----";
    private static final String PUBLIC_KEY_SUFFIX = "-----END PUBLIC KEY-----";
    private final TextEncryptor textEncryptor;

    @Override
    public String convertToDatabaseColumn(RSAPublicKey rsaPublicKey) {
        try {
            if (rsaPublicKey == null) {
                return null;
            }

            X509EncodedKeySpec x509EncodedKeySpec = new X509EncodedKeySpec(rsaPublicKey.getEncoded());
            String encodedKey = Base64.getMimeEncoder().encodeToString(x509EncodedKeySpec.getEncoded());
            String pemFormattedKey = PUBLIC_KEY_PREFIX + "\n" + encodedKey + "\n" + PUBLIC_KEY_SUFFIX;

            return textEncryptor.encrypt(pemFormattedKey);
        } catch (Exception e) {
            throw new IllegalArgumentException("Failed to convert RSAPublicKey to encrypted String.", e);
        }
    }

    @Override
    public RSAPublicKey convertToEntityAttribute(String dbData) {
        try {
            if (dbData == null || dbData.isEmpty()) {
                return null;
            }

            String decryptedData = textEncryptor.decrypt(dbData);
            String encodedKey = decryptedData
                    .replace(PUBLIC_KEY_PREFIX, "")
                    .replace(PUBLIC_KEY_SUFFIX, "")
                    .replaceAll("\\s", "");

            byte[] keyBytes = Base64.getMimeDecoder().decode(encodedKey);
            X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            PublicKey publicKey = keyFactory.generatePublic(keySpec);

            return (RSAPublicKey) publicKey;
        } catch (Exception e) {
            throw new IllegalArgumentException("Failed to convert encrypted String to RSAPublicKey.", e);
        }
    }
}
