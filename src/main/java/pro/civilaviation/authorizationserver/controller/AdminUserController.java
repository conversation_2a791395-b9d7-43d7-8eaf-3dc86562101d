package pro.civilaviation.authorizationserver.controller;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import pro.civilaviation.authorizationserver.dto.*;
import pro.civilaviation.authorizationserver.service.UserDetailsService;

import java.util.UUID;

/**
 * created by <PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>
 */

@RestController
@RequestMapping("/api/admin/users")
public class AdminUserController {

    private final UserDetailsService userDetailsService;

    public AdminUserController(UserDetailsService userDetailsService) {
        Assert.notNull(userDetailsService, "userDetailsService cannot be null");
        this.userDetailsService = userDetailsService;
    }

    @GetMapping("/info/{userId}")
    public ResponseEntity<ResponseUserDto> getUserInfo(@PathVariable UUID userId) {
        ResponseUserDto responseUserDto = userDetailsService.getUserInfo(userId);
        return ResponseEntity.ok(responseUserDto);
    }

    @GetMapping("/search")
    public ResponseEntity<Page<ResponseUsersSearchDto>> searchUsers(
            @RequestBody RequestUsersFilterDto requestUsersFilterDto,
            Pageable pageable){
        Page<ResponseUsersSearchDto> responseUserDtoPage = userDetailsService.searchUsers(requestUsersFilterDto, pageable);
        return ResponseEntity.ok(responseUserDtoPage);
    }

    @PostMapping("/create")
    public ResponseEntity<ResponseUserDto> createUser(@RequestBody RequestNewUserDto requestNewUserDto) {
        ResponseUserDto responseUserDto = userDetailsService.createNewUser(requestNewUserDto);
        return ResponseEntity.status(HttpStatus.CREATED).body(responseUserDto);
    }

    @PutMapping("/update/{userId}")
    public ResponseEntity<ResponseUserDto> updateUser(@PathVariable UUID userId, @RequestBody RequestUpdateUserDto requestUpdateUserDto) {
        ResponseUserDto responseUserDto = userDetailsService.updateUser(userId, requestUpdateUserDto);
        return ResponseEntity.ok(responseUserDto);
    }

    @DeleteMapping("/delete/{userId}")
    public ResponseEntity<Void> deleteUser(@PathVariable UUID userId) {
        userDetailsService.deleteUser(userId);
        return ResponseEntity.noContent().build();
    }

    @PutMapping("/lock/{userId}")
    public ResponseEntity<Void> lockUser(@PathVariable UUID userId) {
        userDetailsService.lockUser(userId);
        return ResponseEntity.noContent().build();
    }

    @PutMapping("/unlock/{userId}")
    public ResponseEntity<Void> unlockUser(@PathVariable UUID userId) {
        userDetailsService.unlockUser(userId);
        return ResponseEntity.noContent().build();
    }
}