package pro.civilaviation.authorizationserver.controller;

import jakarta.servlet.http.HttpServletRequest;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.client.HttpClientErrorException;
import pro.civilaviation.authorizationserver.dto.ResponseErrorDto;
import pro.civilaviation.authorizationserver.exception.UserAlreadyExistException;
import pro.civilaviation.authorizationserver.exception.UserNotFoundException;

import java.time.Instant;

/**
 * created by <PERSON><PERSON>r_Nikolskiy
 */

@ControllerAdvice
public class GlobalExceptionHandlerController {

     @ExceptionHandler({UserAlreadyExistException.class, UserNotFoundException.class})
     public ResponseEntity<ResponseErrorDto> handleBadRequest(HttpServletRequest req, Exception ex){
          HttpStatus httpStatus = HttpStatus.BAD_REQUEST;
          ResponseErrorDto responseErrorDto = new ResponseErrorDto(
                  Instant.now(),
                  httpStatus.value(),
                  httpStatus.getReasonPhrase(),
                  ex.getMessage(),
                  req.getRequestURI()
          );
          return ResponseEntity.status(httpStatus).body(responseErrorDto);
     }
}
