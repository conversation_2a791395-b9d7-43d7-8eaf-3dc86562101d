package pro.civilaviation.authorizationserver.service;

import com.nimbusds.jose.jwk.JWK;
import com.nimbusds.jose.jwk.JWKSelector;
import com.nimbusds.jose.jwk.RSAKey;
import com.nimbusds.jose.jwk.source.JWKSource;
import com.nimbusds.jose.proc.SecurityContext;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import pro.civilaviation.authorizationserver.domain.entity.RsaKeyPairEntity;
import pro.civilaviation.authorizationserver.repository.RsaKeyPairRepository;

import java.util.ArrayList;
import java.util.List;

/**
 * created by <PERSON>andr_Nikolskiy
 */

@Service
@RequiredArgsConstructor
public class JwkSourceService implements JWKSource<SecurityContext> {

    private final RsaKeyPairRepository rsaKeyPairRepository;

    @Override
    public List<JWK> get(JWKSelector jwkSelector, SecurityContext context) {
        List<RsaKeyPairEntity> keyPairsFromRepo = rsaKeyPairRepository.findAll();
        ArrayList<JWK> result = new ArrayList<>(keyPairsFromRepo.size());
        for (RsaKeyPairEntity rsaKeyPair: keyPairsFromRepo) {
            RSAKey rsaKey = new RSAKey.Builder(rsaKeyPair.getRsaPublicKey())
                    .keyID(rsaKeyPair.getId().toString())
                    .privateKey(rsaKeyPair.getRsaPrivateKey())
                    .build();

            if (jwkSelector.getMatcher().matches(rsaKey)) {
                result.add(rsaKey);
            }

        }
        return result;
    }
}
