package pro.civilaviation.authorizationserver.service;

import jakarta.annotation.PostConstruct;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import pro.civilaviation.authorizationserver.config.data.TenantConfigData;

import java.util.UUID;

/**
 * created by <PERSON>andr_Nikolskiy
 */

@Service
public class TenantService {
    private final TenantConfigData tenantConfigData;


    public TenantService(TenantConfigData tenantConfigData) {
        Assert.notNull(tenantConfigData, "tenantConfigData cannot be null");
        this.tenantConfigData = tenantConfigData;
    }

    @PostConstruct
    public void fillTenantIds() {
        tenantConfigData.tenants().forEach((id, tc) -> tc.setId(id));
    }
}
