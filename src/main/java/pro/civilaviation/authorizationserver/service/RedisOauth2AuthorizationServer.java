package pro.civilaviation.authorizationserver.service;

import lombok.RequiredArgsConstructor;
import org.springframework.security.oauth2.core.endpoint.OAuth2ParameterNames;
import org.springframework.security.oauth2.core.oidc.endpoint.OidcParameterNames;
import org.springframework.security.oauth2.server.authorization.OAuth2Authorization;
import org.springframework.security.oauth2.server.authorization.OAuth2AuthorizationService;
import org.springframework.security.oauth2.server.authorization.OAuth2TokenType;
import org.springframework.security.oauth2.server.authorization.client.RegisteredClientRepository;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import pro.civilaviation.authorizationserver.domain.converter.OAuth2AuthorizationConverter;
import pro.civilaviation.authorizationserver.domain.entity.RedisOAuth2Authorization;
import pro.civilaviation.authorizationserver.repository.RedisOAuth2AuthorizationRepository;

import java.util.Optional;

/**
 * created by Alexandr_Nikolskiy
 */

@Service
@RequiredArgsConstructor
public class RedisOauth2AuthorizationServer implements OAuth2AuthorizationService {

    private final RedisOAuth2AuthorizationRepository authRepository;
    private final RegisteredClientRepository registeredClientRepository;

    @Override
    public void save(OAuth2Authorization authorization) {
        Assert.notNull(authorization, "authorization cannot be null");
        authRepository.save(OAuth2AuthorizationConverter.toRedisEntity(authorization));
    }

    @Override
    public void remove(OAuth2Authorization authorization) {
        Assert.notNull(authorization, "authorization cannot be null");
        authRepository.deleteById(authorization.getId());
    }

    @Override
    public OAuth2Authorization findById(String id) {
        Assert.hasText(id, "id cannot be empty");
        return authRepository.findById(id)
                .map(entity -> OAuth2AuthorizationConverter.fromRedisEntity(entity, registeredClientRepository))
                .orElse(null);
    }

    @Override
    public OAuth2Authorization findByToken(String token, OAuth2TokenType tokenType) {
        Assert.hasText(token, "token cannot be empty");
        assert tokenType != null;
        Optional<RedisOAuth2Authorization> result = switch (tokenType.getValue()) {
            case OAuth2ParameterNames.STATE -> authRepository.findByState(token);
            case OAuth2ParameterNames.CODE -> authRepository.findByAuthorizationCodeValue(token);
            case OAuth2ParameterNames.ACCESS_TOKEN -> authRepository.findByAccessTokenValue(token);
            case OAuth2ParameterNames.REFRESH_TOKEN -> authRepository.findByRefreshTokenValue(token);
            case OidcParameterNames.ID_TOKEN -> authRepository.findByOidcIdTokenValue(token);
            case OAuth2ParameterNames.USER_CODE -> authRepository.findByUserCodeValue(token);
            case OAuth2ParameterNames.DEVICE_CODE -> authRepository.findByDeviceCodeValue(token);
            default -> Optional.empty();
        };

        return result.map(entity -> OAuth2AuthorizationConverter.fromRedisEntity(entity, registeredClientRepository)).orElse(null);
    }
}
