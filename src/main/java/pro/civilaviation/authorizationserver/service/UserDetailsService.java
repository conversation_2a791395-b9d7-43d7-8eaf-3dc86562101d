package pro.civilaviation.authorizationserver.service;

import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import pro.civilaviation.authorizationserver.domain.entity.UserEntity;
import pro.civilaviation.authorizationserver.domain.specification.UsersSearchSpecification;
import pro.civilaviation.authorizationserver.dto.*;
import pro.civilaviation.authorizationserver.exception.UserAlreadyExistException;
import pro.civilaviation.authorizationserver.exception.UserNotFoundException;
import pro.civilaviation.authorizationserver.mapper.NewUserDtoMapper;
import pro.civilaviation.authorizationserver.mapper.ResponseSearchUsersDtoMapper;
import pro.civilaviation.authorizationserver.mapper.ResponseUserDtoMapper;
import pro.civilaviation.authorizationserver.mapper.UpdateUserDtoMapper;
import pro.civilaviation.authorizationserver.repository.UserRepository;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;


/**
 * created by Alexandr_Nikolskiy
 */

//TODO preauthorize
@Service
@Transactional
@RequiredArgsConstructor
public class UserDetailsService implements org.springframework.security.core.userdetails.UserDetailsService {

    private final UserRepository userRepository;
    private final ResponseUserDtoMapper responseUserDtoMapper;
    private final ResponseSearchUsersDtoMapper responseSearchUsersDtoMapper;
    private final NewUserDtoMapper newUserDtoMapper;
    private final UpdateUserDtoMapper updateUserDtoMapper;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        UserEntity userEntity = userRepository.findUserWithAuthoritiesByUsername(username)
                .orElseThrow(() -> new UsernameNotFoundException(String.format("User with name %s not found", username)));

        Set<SimpleGrantedAuthority> grantedAuthorities = userEntity.getAuthorityEntities()
                .stream()
                .map(authorityEntity ->
                        new SimpleGrantedAuthority(authorityEntity.getAuthority()))
                .collect(Collectors.toSet());

        return User
                .withUsername(userEntity.getUsername())
                .password(userEntity.getPassword())
                .accountExpired(false)
                .accountLocked(userEntity.getIsAccountLocked())
                .credentialsExpired(false)
                .disabled(false)
                .authorities(grantedAuthorities)
                .build();
    }

    public ResponseUserDto getUserInfo(UUID userId) {
        UserEntity userEntity = userRepository.findUserWithAuthoritiesById(userId)
                .orElseThrow(() -> new UserNotFoundException(String.format("User with id %s not found", userId)));
        return responseUserDtoMapper.toDto(userEntity);
    }

    public Page<ResponseUsersSearchDto> searchUsers(RequestUsersFilterDto requestUsersFilterDto, Pageable pageable) {
        Specification<UserEntity> spec = UsersSearchSpecification.withAllFilters(requestUsersFilterDto);
        Page<UserEntity> userEntityPage = userRepository.findAll(spec, pageable);
        return userEntityPage.map(responseSearchUsersDtoMapper::toDto);
    }

    public ResponseUserDto createNewUser(RequestNewUserDto requestNewUserDto) {
        //TODO validate RequestNewUserDto
        if (userRepository.existsByUsername(requestNewUserDto.username())) {
            throw new UserAlreadyExistException(String.format("User with name %s already exist", requestNewUserDto.username()));
        }
        UserEntity userEntity = newUserDtoMapper.toEntity(requestNewUserDto);
        userEntity = userRepository.save(userEntity);
        return responseUserDtoMapper.toDto(userEntity);
    }

    public ResponseUserDto updateUser(UUID userId, RequestUpdateUserDto requestUpdateUserDto) {
        //TODO validate RequestUpdateUserDto
        UserEntity userEntity = userRepository.findUserWithAuthoritiesById(userId)
                .orElseThrow(() -> new UserNotFoundException(String.format("User with id %s not found", userId)));
        updateUserDtoMapper.partialUpdate(requestUpdateUserDto, userEntity);
        userRepository.save(userEntity);
        return responseUserDtoMapper.toDto(userEntity);
    }

    public void deleteUser(UUID userId) {
        //TODO invalidate session?
        if (userRepository.existsById(userId)) {
            userRepository.deleteById(userId);
        } else {
            throw new UserNotFoundException(String.format("User with id %s not found", userId));
        }
    }

    public void lockUser(UUID userId) {
        //TODO invalidate session?
        UserEntity userEntity = userRepository.findById(userId)
                .orElseThrow(() -> new UserNotFoundException(String.format("User with id %s not found", userId)));
        userEntity.setIsAccountLocked(true);
        userRepository.save(userEntity);
    }

    public void unlockUser(UUID userId) {
        UserEntity userEntity = userRepository.findById(userId)
                .orElseThrow(() -> new UserNotFoundException(String.format("User with id %s not found", userId)));
        userEntity.setIsAccountLocked(false);
        userRepository.save(userEntity);
    }
}
