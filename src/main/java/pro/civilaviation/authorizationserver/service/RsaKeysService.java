package pro.civilaviation.authorizationserver.service;

import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import pro.civilaviation.authorizationserver.domain.entity.RsaKeyPairEntity;
import pro.civilaviation.authorizationserver.repository.RsaKeyPairRepository;

import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.time.Instant;
import java.util.UUID;

/**
 * created by <PERSON><PERSON>r_Nik<PERSON>kiy
 */

@Service
@RequiredArgsConstructor
public class RsaKeysService {

    private final RsaKeyPairRepository rsaKeyPairRepository;

    @Transactional
    public void createRsaKeyPairIfNotExist() {
        if (rsaKeyPairRepository.findAll().isEmpty()) {
            RsaKeyPairEntity rsaKeyPair = this.generateRsaKeyPair();
            rsaKeyPairRepository.saveAndFlush(rsaKeyPair);
        }
    }

    private RsaKeyPairEntity generateRsaKeyPair() {
        KeyPair keyPair = this.generateRsaKey();
        RSAPublicKey publicKey = (RSAPublicKey) keyPair.getPublic();
        RSAPrivateKey privateKey = (RSAPrivateKey) keyPair.getPrivate();
        return new RsaKeyPairEntity()
                .setId(UUID.randomUUID())
                .setCreatedAt(Instant.now())
                .setRsaPublicKey(publicKey)
                .setRsaPrivateKey(privateKey);
    }

    private KeyPair generateRsaKey() {
        try {
            KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance("RSA");
            keyPairGenerator.initialize(2048);
            return keyPairGenerator.generateKeyPair();
        }
        catch (Exception ex) {
            throw new IllegalStateException(ex);
        }
    }
}
