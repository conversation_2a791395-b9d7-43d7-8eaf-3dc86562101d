package pro.civilaviation.authorizationserver.service;

import jakarta.transaction.Transactional;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.oauth2.core.AuthorizationGrantType;
import org.springframework.security.oauth2.core.ClientAuthenticationMethod;
import org.springframework.security.oauth2.core.oidc.OidcScopes;
import org.springframework.security.oauth2.server.authorization.client.RegisteredClient;
import org.springframework.security.oauth2.server.authorization.client.RegisteredClientRepository;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import pro.civilaviation.authorizationserver.config.data.RegisteredClientConfigData;

import java.time.Instant;
import java.util.UUID;

/**
 * created by Alexandr_Nikolskiy
 */

@Service
public class RegisteredClientService {

    private final RegisteredClientConfigData registeredClientConfigData;
    private final RegisteredClientRepository registeredClientRepository;
    private final PasswordEncoder passwordEncoder;


    public RegisteredClientService(RegisteredClientConfigData registeredClientConfigData,
                                   RegisteredClientRepository registeredClientRepository,
                                   PasswordEncoder passwordEncoder) {
        Assert.notNull(registeredClientConfigData, "registeredClientConfigData cannot be null");
        Assert.notNull(registeredClientRepository, "registeredClientRepository cannot be null");
        Assert.notNull(passwordEncoder, "passwordEncoder cannot be null");

        this.registeredClientConfigData = registeredClientConfigData;
        this.registeredClientRepository = registeredClientRepository;
        this.passwordEncoder = passwordEncoder;
    }

    @Transactional
    public void initRegisteredClient() {
        // First, try to find the client with the configured client ID
        RegisteredClient registeredClient = registeredClientRepository
                .findByClientId(registeredClientConfigData.clientId());

        // If the configured client doesn't exist, create it
        if (registeredClient == null) {
            RegisteredClient.Builder clientBuilder = RegisteredClient
                    .withId(UUID.randomUUID().toString())
                    .clientId(registeredClientConfigData.clientId())
                    .clientIdIssuedAt(Instant.now())
                    .clientSecret(passwordEncoder.encode(registeredClientConfigData.clientSecret()))
                    .clientAuthenticationMethod(ClientAuthenticationMethod.CLIENT_SECRET_BASIC)
                    .authorizationGrantType(AuthorizationGrantType.AUTHORIZATION_CODE)
                    .authorizationGrantType(AuthorizationGrantType.REFRESH_TOKEN)
                    .scope(OidcScopes.OPENID)
                    // Add the configured redirect URI
                    .redirectUri(registeredClientConfigData.redirectUri())
                    .postLogoutRedirectUri(registeredClientConfigData.postLogoutRedirectUri());

            // Add all additional redirect URIs
            if (registeredClientConfigData.additionalRedirectUris() != null) {
                for (String additionalUri : registeredClientConfigData.additionalRedirectUris()) {
                    clientBuilder.redirectUri(additionalUri);
                }
            }

            RegisteredClient defaultClient = clientBuilder.build();

            registeredClientRepository.save(defaultClient);
            System.out.println("Created client with ID: " + registeredClientConfigData.clientId());
        }
    }
}
