package pro.civilaviation.authorizationserver.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;
import pro.civilaviation.authorizationserver.domain.entity.AuthorityEntity;
import pro.civilaviation.authorizationserver.domain.entity.UserEntity;
import pro.civilaviation.authorizationserver.dto.RequestNewUserDto;

import java.util.Set;
import java.util.stream.Collectors;

/**
 * created by <PERSON><PERSON><PERSON><PERSON>
 */

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = MappingConstants.ComponentModel.SPRING)
public interface NewUserDtoMapper {

     @Mapping(target = "authorityEntities", expression = "java(authoritiesFromDtoToEntities(requestNewUserDto, userEntity))")
     @Mapping(target = "isAccountLocked", expression = "java(false)")
     UserEntity toEntity(RequestNewUserDto requestNewUserDto);

     default Set<AuthorityEntity> authoritiesFromDtoToEntities(RequestNewUserDto requestNewUserDto, UserEntity userEntity) {
          return requestNewUserDto.authorities().stream()
                  .map(a -> {
                       AuthorityEntity authorityEntity = new AuthorityEntity();
                       authorityEntity.setAuthority(a);
                       authorityEntity.setUserEntity(userEntity);
                       return authorityEntity;
                  })
                  .collect(Collectors.toSet());
     }
}
