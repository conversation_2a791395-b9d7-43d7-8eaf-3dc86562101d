package pro.civilaviation.authorizationserver.mapper;

import org.mapstruct.*;
import pro.civilaviation.authorizationserver.domain.entity.AuthorityEntity;
import pro.civilaviation.authorizationserver.domain.entity.UserEntity;
import pro.civilaviation.authorizationserver.dto.ResponseUserDto;

import java.util.Set;
import java.util.stream.Collectors;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = MappingConstants.ComponentModel.SPRING)
public interface ResponseUserDtoMapper {

    @Mapping(target = "authorities", expression = "java(authorityEntitiesToDtoAuthorities(userEntity.getAuthorityEntities()))")
    ResponseUserDto toDto(UserEntity userEntity);

    default Set<String> authorityEntitiesToDtoAuthorities(Set<AuthorityEntity> authorityEntities) {
        return authorityEntities.stream().map(AuthorityEntity::getAuthority).collect(Collectors.toSet());
    }
}