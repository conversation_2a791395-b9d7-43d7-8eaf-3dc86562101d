package pro.civilaviation.authorizationserver.mapper;

import org.mapstruct.*;
import pro.civilaviation.authorizationserver.dto.RequestUpdateUserDto;
import pro.civilaviation.authorizationserver.domain.entity.AuthorityEntity;
import pro.civilaviation.authorizationserver.domain.entity.UserEntity;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = MappingConstants.ComponentModel.SPRING)
public interface UpdateUserDtoMapper {

    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    default UserEntity partialUpdate(RequestUpdateUserDto requestUpdateUserDto, @MappingTarget UserEntity userEntity) {
        if (requestUpdateUserDto == null) {
            return userEntity;
        }
        
        if (requestUpdateUserDto.username() != null) {
            userEntity.setUsername(requestUpdateUserDto.username());
        }
        
        if (requestUpdateUserDto.password() != null) {
            userEntity.setPassword(requestUpdateUserDto.password());
        }
        
        if (requestUpdateUserDto.isAccountLocked() != null) {
            userEntity.setIsAccountLocked(requestUpdateUserDto.isAccountLocked());
        }
        
        if (requestUpdateUserDto.authorities() != null) {
            userEntity.getAuthorityEntities().clear();
            for (String authority : requestUpdateUserDto.authorities()) {
                AuthorityEntity authorityEntity = new AuthorityEntity();
                authorityEntity.setAuthority(authority);
                authorityEntity.setUserEntity(userEntity);
                userEntity.getAuthorityEntities().add(authorityEntity);
            }
        }
        return userEntity;
    }

}