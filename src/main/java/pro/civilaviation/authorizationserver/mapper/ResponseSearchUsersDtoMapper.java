package pro.civilaviation.authorizationserver.mapper;

import org.mapstruct.*;
import pro.civilaviation.authorizationserver.domain.entity.UserEntity;
import pro.civilaviation.authorizationserver.dto.ResponseUsersSearchDto;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = MappingConstants.ComponentModel.SPRING)
public interface ResponseSearchUsersDtoMapper {
    ResponseUsersSearchDto toDto(UserEntity userEntity);
}