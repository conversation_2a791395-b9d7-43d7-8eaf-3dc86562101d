package pro.civilaviation.authorizationserver.init;

import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import org.springframework.core.task.TaskExecutor;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import pro.civilaviation.authorizationserver.config.data.TenantConfigData;
import pro.civilaviation.authorizationserver.domain.entity.AuthorityEntity;
import pro.civilaviation.authorizationserver.domain.entity.UserEntity;
import pro.civilaviation.authorizationserver.multitent.TenantContext;
import pro.civilaviation.authorizationserver.multitent.TenantContextHolder;
import pro.civilaviation.authorizationserver.repository.UserRepository;

import java.util.HashSet;
import java.util.Optional;
import java.util.UUID;

/**
 * Component to ensure there's always a default admin user in each tenant's database
 */
@Component
@RequiredArgsConstructor
public class DefaultUserInitializer {

    private final UserRepository userRepository;
    private final PasswordEncoder passwordEncoder;
    private final TenantConfigData tenantConfigData;
    private final TaskExecutor taskExecutor;

    @PostConstruct
    public void init() {
        // Initialize default admin user for each tenant asynchronously
        tenantConfigData.tenants().forEach((id, tenantContext) -> {
            taskExecutor.execute(() -> initializeDefaultUserForTenant(id, tenantContext));
        });
    }

    private void initializeDefaultUserForTenant(UUID tenantId, TenantContext tenantContext) {
        try {
            // Set the tenant context
            TenantContextHolder.setCurrentTenant(tenantContext);

            // Create default admin user for this tenant
            createDefaultAdminUserIfNotExists();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            // Clear the tenant context
            TenantContextHolder.clear();
        }
    }

    @Transactional
    public void createDefaultAdminUserIfNotExists() {
        try {
            // Check if admin user already exists
            Optional<UserEntity> existingAdmin = userRepository.findUserWithAuthoritiesByUsername("admin");

            if (existingAdmin.isPresent()) {
                return;
            }
            // Create new admin user
            UserEntity adminUser = new UserEntity();
            adminUser.setUsername("admin");
            adminUser.setPassword(passwordEncoder.encode("admin")); // Default password: admin
            adminUser.setIsAccountLocked(false);
            adminUser.setAuthorityEntities(new HashSet<>());

            // Create admin authority
            AuthorityEntity adminAuthority = new AuthorityEntity();
            adminAuthority.setAuthority("ROLE_ADMIN");
            adminAuthority.setUserEntity(adminUser);

            // Add authority to user
            adminUser.getAuthorityEntities().add(adminAuthority);

            // Save user
            userRepository.save(adminUser);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
