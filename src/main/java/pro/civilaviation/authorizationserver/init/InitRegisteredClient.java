package pro.civilaviation.authorizationserver.init;

import lombok.RequiredArgsConstructor;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Component;
import pro.civilaviation.authorizationserver.config.data.TenantConfigData;
import pro.civilaviation.authorizationserver.multitent.TenantContext;
import pro.civilaviation.authorizationserver.multitent.TenantContextHolder;
import pro.civilaviation.authorizationserver.service.RegisteredClientService;

/**
 * created by <PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>y
 */

@Component
@RequiredArgsConstructor
public class InitRegisteredClient implements ApplicationRunner {

    private final TaskExecutor taskExecutor;
    private final RegisteredClientService registeredClientService;
    private final TenantConfigData tenantConfigData;

    @Override
    public void run(ApplicationArguments args) {
        tenantConfigData.tenants().forEach((id, tc) -> {
            taskExecutor.execute(new initRegisteredClientTask(tc));
        });
    }



    public class initRegisteredClientTask implements Runnable {

        private final TenantContext tc;

        public initRegisteredClientTask(TenantContext tc) {
            this.tc = tc;
        }

        @Override
        public void run() {
            TenantContextHolder.setCurrentTenant(tc);
            registeredClientService.initRegisteredClient();
            TenantContextHolder.clear();
        }
    }
}
