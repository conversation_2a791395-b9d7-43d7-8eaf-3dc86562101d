package pro.civilaviation.authorizationserver.init;

import lombok.RequiredArgsConstructor;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Component;
import pro.civilaviation.authorizationserver.config.data.TenantConfigData;
import pro.civilaviation.authorizationserver.multitent.TenantContext;
import pro.civilaviation.authorizationserver.multitent.TenantContextHolder;
import pro.civilaviation.authorizationserver.service.RsaKeysService;

/**
 * created by <PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>
 */

@Component
@RequiredArgsConstructor
public class InitRsaKeyPairs implements ApplicationRunner {

    private final RsaKeysService rsaKeysService;
    private final TenantConfigData tenantConfigData;
    private final TaskExecutor taskExecutor;

    @Override
    public void run(ApplicationArguments args) {

        tenantConfigData.tenants().forEach((id, tc) -> {
            taskExecutor.execute(new CreateRsaKeysTask(tc));
        });
    }

    public class CreateRsaKeysTask implements Runnable {

        private final TenantContext tc;

        public CreateRsaKeysTask(TenantContext tc) {
            this.tc = tc;
        }

        @Override
        public void run() {
            TenantContextHolder.setCurrentTenant(tc);
            rsaKeysService.createRsaKeyPairIfNotExist();
            TenantContextHolder.clear();
        }
    }
}
