package pro.civilaviation.authorizationserver.repository;

import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;
import pro.civilaviation.authorizationserver.domain.entity.UserEntity;
import pro.civilaviation.authorizationserver.domain.entity.UserEntity_;

import java.util.Optional;
import java.util.UUID;

@Repository
public interface UserRepository extends JpaRepository<UserEntity, UUID>, JpaSpecificationExecutor<UserEntity> {

    @EntityGraph(UserEntity_.GRAPH_USER_WITH_AUTHORITIES)
    Optional<UserEntity> findUserWithAuthoritiesByUsername(String username);

    @EntityGraph(UserEntity_.GRAPH_USER_WITH_AUTHORITIES)
    Optional<UserEntity> findUserWithAuthoritiesById(UUID id);

    boolean existsByUsername(String username);
}
