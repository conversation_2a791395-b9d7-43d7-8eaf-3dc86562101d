package pro.civilaviation.authorizationserver.repository;

import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;
import pro.civilaviation.authorizationserver.domain.entity.RedisOAuth2Authorization;

import java.util.Optional;

/**
 * created by <PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>y
 */

@Repository
public interface RedisOAuth2AuthorizationRepository extends CrudRepository<RedisOAuth2Authorization, String> {
    Optional<RedisOAuth2Authorization> findByAuthorizationCodeValue(String authorizationCodeValue);
    Optional<RedisOAuth2Authorization> findByAccessTokenValue(String accessTokenValue);
    Optional<RedisOAuth2Authorization> findByRefreshTokenValue(String refreshTokenValue);
    Optional<RedisOAuth2Authorization> findByOidcIdTokenValue(String oidcIdTokenValue);
    Optional<RedisOAuth2Authorization> findByUserCodeValue(String userCodeValue);
    Optional<RedisOAuth2Authorization> findByDeviceCodeValue(String deviceCodeValue);
    Optional<RedisOAuth2Authorization> findByState(String state);
}