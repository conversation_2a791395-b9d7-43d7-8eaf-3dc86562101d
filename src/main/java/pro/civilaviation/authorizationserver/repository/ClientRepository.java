package pro.civilaviation.authorizationserver.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import pro.civilaviation.authorizationserver.domain.entity.ClientEntity;

import java.util.Optional;

/**
 * created by <PERSON><PERSON><PERSON><PERSON>
 */

@Repository
public interface ClientRepository extends JpaRepository<ClientEntity, String> {
    Optional<ClientEntity> findByClientId(String s);
}