--liquibase formatted sql

--changeset a.nikolskiy:create_default_admin_user

INSERT INTO users (id, username, password, is_account_locked)
VALUES (
    uuid_generate_v4(),
    'admin',
    '{bcrypt}$2a$10$4NQLQ4b0U09MeovkkS1mCuHz.MANW2XLJnVH0xr5G4HtlDTK31TOG',
    false
);

INSERT INTO authorities (id, user_id, authority)
VALUES (
    uuid_generate_v4(),
    (SELECT id FROM users WHERE username = 'admin'),
    'ROLE_ADMIN'
);

